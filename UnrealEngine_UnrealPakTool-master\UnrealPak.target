{"TargetName": "UnrealPak", "Platform": "Win64", "Configuration": "Development", "TargetType": "Program", "Architecture": "", "Launch": "$(EngineDir)/Binaries/Win64/UnrealPak.exe", "Version": {"MajorVersion": 4, "MinorVersion": 27, "PatchVersion": 0, "Changelist": 17155196, "CompatibleChangelist": 0, "IsLicenseeVersion": 0, "IsPromotedBuild": 1, "BranchName": "++UE4+Release-4.27", "BuildId": "17155196"}, "BuildProducts": [{"Path": "$(EngineDir)/Binaries/Win64/UnrealPak.exe", "Type": "Executable"}, {"Path": "$(EngineDir)/Binaries/Win64/UnrealPak.pdb", "Type": "SymbolFile"}, {"Path": "$(EngineDir)/Plugins/Compression/OodleData/Binaries/Win64/UnrealPak-OodleDataCompressionFormat.dll", "Type": "DynamicLibrary"}, {"Path": "$(EngineDir)/Plugins/Compression/OodleData/Binaries/Win64/UnrealPak-OodleDataCompressionFormat.pdb", "Type": "SymbolFile"}, {"Path": "$(EngineDir)/Plugins/Experimental/PlatformCrypto/Binaries/Win64/UnrealPak-PlatformCrypto.dll", "Type": "DynamicLibrary"}, {"Path": "$(EngineDir)/Plugins/Experimental/PlatformCrypto/Binaries/Win64/UnrealPak-PlatformCrypto.pdb", "Type": "SymbolFile"}, {"Path": "$(EngineDir)/Plugins/Experimental/PlatformCrypto/Binaries/Win64/UnrealPak-PlatformCryptoTypes.dll", "Type": "DynamicLibrary"}, {"Path": "$(EngineDir)/Plugins/Experimental/PlatformCrypto/Binaries/Win64/UnrealPak-PlatformCryptoTypes.pdb", "Type": "SymbolFile"}, {"Path": "$(EngineDir)/Plugins/Experimental/PlatformCrypto/Binaries/Win64/UnrealPak-PlatformCryptoOpenSSL.dll", "Type": "DynamicLibrary"}, {"Path": "$(EngineDir)/Plugins/Experimental/PlatformCrypto/Binaries/Win64/UnrealPak-PlatformCryptoOpenSSL.pdb", "Type": "SymbolFile"}, {"Path": "$(EngineDir)/Binaries/Win64/UnrealPak-BuildSettings.dll", "Type": "DynamicLibrary"}, {"Path": "$(EngineDir)/Binaries/Win64/UnrealPak-BuildSettings.pdb", "Type": "SymbolFile"}, {"Path": "$(EngineDir)/Binaries/Win64/UnrealPak-TraceLog.dll", "Type": "DynamicLibrary"}, {"Path": "$(EngineDir)/Binaries/Win64/UnrealPak-TraceLog.pdb", "Type": "SymbolFile"}, {"Path": "$(EngineDir)/Binaries/Win64/UnrealPak-Core.dll", "Type": "DynamicLibrary"}, {"Path": "$(EngineDir)/Binaries/Win64/UnrealPak-Core.pdb", "Type": "SymbolFile"}, {"Path": "$(EngineDir)/Binaries/Win64/UnrealPak-RSA.dll", "Type": "DynamicLibrary"}, {"Path": "$(EngineDir)/Binaries/Win64/UnrealPak-RSA.pdb", "Type": "SymbolFile"}, {"Path": "$(EngineDir)/Binaries/Win64/UnrealPak-PakFile.dll", "Type": "DynamicLibrary"}, {"Path": "$(EngineDir)/Binaries/Win64/UnrealPak-PakFile.pdb", "Type": "SymbolFile"}, {"Path": "$(EngineDir)/Binaries/Win64/UnrealPak-Json.dll", "Type": "DynamicLibrary"}, {"Path": "$(EngineDir)/Binaries/Win64/UnrealPak-Json.pdb", "Type": "SymbolFile"}, {"Path": "$(EngineDir)/Binaries/Win64/UnrealPak-Projects.dll", "Type": "DynamicLibrary"}, {"Path": "$(EngineDir)/Binaries/Win64/UnrealPak-Projects.pdb", "Type": "SymbolFile"}, {"Path": "$(EngineDir)/Binaries/Win64/UnrealPak-SSL.dll", "Type": "DynamicLibrary"}, {"Path": "$(EngineDir)/Binaries/Win64/UnrealPak-SSL.pdb", "Type": "SymbolFile"}, {"Path": "$(EngineDir)/Binaries/Win64/UnrealPak-DerivedDataCache.dll", "Type": "DynamicLibrary"}, {"Path": "$(EngineDir)/Binaries/Win64/UnrealPak-DerivedDataCache.pdb", "Type": "SymbolFile"}, {"Path": "$(EngineDir)/Binaries/Win64/UnrealPak-PakFileUtilities.dll", "Type": "DynamicLibrary"}, {"Path": "$(EngineDir)/Binaries/Win64/UnrealPak-PakFileUtilities.pdb", "Type": "SymbolFile"}, {"Path": "$(EngineDir)/Binaries/Win64/UnrealPak-CoreUObject.dll", "Type": "DynamicLibrary"}, {"Path": "$(EngineDir)/Binaries/Win64/UnrealPak-CoreUObject.pdb", "Type": "SymbolFile"}, {"Path": "$(EngineDir)/Binaries/Win64/UnrealPak-Analytics.dll", "Type": "DynamicLibrary"}, {"Path": "$(EngineDir)/Binaries/Win64/UnrealPak-Analytics.pdb", "Type": "SymbolFile"}, {"Path": "$(EngineDir)/Binaries/Win64/UnrealPak.version", "Type": "RequiredResource"}, {"Path": "$(EngineDir)/Plugins/Compression/OodleData/Binaries/Win64/UnrealPak.modules", "Type": "RequiredResource"}, {"Path": "$(EngineDir)/Plugins/Experimental/PlatformCrypto/Binaries/Win64/UnrealPak.modules", "Type": "RequiredResource"}, {"Path": "$(EngineDir)/Binaries/Win64/UnrealPak.modules", "Type": "RequiredResource"}], "RuntimeDependencies": [{"Path": "$(EngineDir)/Plugins/Compression/OodleData/OodleData.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Plugins/Experimental/PlatformCrypto/PlatformCrypto.uplugin", "Type": "UFS"}, {"Path": "$(EngineDir)/Binaries/ThirdParty/DbgHelp/dbghelp.dll", "Type": "NonUFS"}], "AdditionalProperties": [{"Name": "SDK", "Value": "Not Applicable"}]}